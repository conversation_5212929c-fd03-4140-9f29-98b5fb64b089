{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\cafe-manager\\\\src\\\\pages\\\\OrdersPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport MenuItem from '../components/MenuItem';\nimport InvoiceItem from '../components/InvoiceItem';\nimport { menuItems } from '../data/menuItems';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OrdersPage = () => {\n  _s();\n  const {\n    tableId\n  } = useParams();\n  const navigate = useNavigate();\n  const [invoiceItems, setInvoiceItems] = useState([]);\n  const handleAddItem = item => {\n    setInvoiceItems(prev => {\n      const existing = prev.find(i => i.item.id === item.id);\n      if (existing) {\n        return prev.map(i => i.item.id === item.id ? {\n          ...i,\n          quantity: i.quantity + 1\n        } : i);\n      }\n      return [...prev, {\n        item,\n        quantity: 1\n      }];\n    });\n  };\n  const handleRemoveItem = id => {\n    setInvoiceItems(prev => prev.filter(item => item.item.id !== id));\n  };\n  const total = invoiceItems.reduce((sum, item) => sum + item.item.price * item.quantity, 0);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-4 bg-amber-50 min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/'),\n        className: \"bg-amber-700 text-white px-4 py-2 rounded-lg\",\n        children: \"\\u2190 \\u0627\\u0644\\u0639\\u0648\\u062F\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-amber-800\",\n        children: [\"\\u0627\\u0644\\u0637\\u0627\\u0648\\u0644\\u0629 \\u0631\\u0642\\u0645: \", tableId]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:col-span-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-4 text-amber-700\",\n          children: \"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0637\\u0644\\u0628\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n          children: menuItems.map(item => /*#__PURE__*/_jsxDEV(MenuItem, {\n            item: item,\n            onAdd: () => handleAddItem(item)\n          }, item.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg shadow-lg\",\n        id: \"invoice\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-4 text-amber-700\",\n          children: \"\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629 \\u0627\\u0644\\u0637\\u0627\\u0648\\u0644\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 max-h-96 overflow-y-auto\",\n          children: invoiceItems.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 text-center py-4\",\n            children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0623\\u0635\\u0646\\u0627\\u0641 \\u0641\\u064A \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this) : invoiceItems.map(invoiceItem => /*#__PURE__*/_jsxDEV(InvoiceItem, {\n            item: invoiceItem.item,\n            quantity: invoiceItem.quantity,\n            onRemove: () => handleRemoveItem(invoiceItem.item.id)\n          }, invoiceItem.item.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t pt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between font-bold text-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u0627\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [total.toLocaleString(), \" \\u062F\\u064A\\u0646\\u0627\\u0631\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.print(),\n            className: \"w-full mt-4 bg-amber-600 text-white py-2 rounded-lg hover:bg-amber-700\",\n            children: \"\\u0637\\u0628\\u0627\\u0639\\u0629 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(OrdersPage, \"dQqjbgPoTq/5p4DjGebi5Ja459Y=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = OrdersPage;\nexport default OrdersPage;\nvar _c;\n$RefreshReg$(_c, \"OrdersPage\");", "map": {"version": 3, "names": ["React", "useState", "useParams", "useNavigate", "MenuItem", "InvoiceItem", "menuItems", "jsxDEV", "_jsxDEV", "OrdersPage", "_s", "tableId", "navigate", "invoiceItems", "setInvoiceItems", "handleAddItem", "item", "prev", "existing", "find", "i", "id", "map", "quantity", "handleRemoveItem", "filter", "total", "reduce", "sum", "price", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onAdd", "length", "invoiceItem", "onRemove", "toLocaleString", "window", "print", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/cafe-manager/src/pages/OrdersPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport MenuItem from '../components/MenuItem';\r\nimport InvoiceItem from '../components/InvoiceItem';\r\nimport { menuItems, MenuItemType } from '../data/menuItems';\r\n\r\nconst OrdersPage: React.FC = () => {\r\n  const { tableId } = useParams<{ tableId: string }>();\r\n  const navigate = useNavigate();\r\n  const [invoiceItems, setInvoiceItems] = useState<{item: MenuItemType, quantity: number}[]>([]);\r\n\r\n  const handleAddItem = (item: MenuItemType) => {\r\n    setInvoiceItems(prev => {\r\n      const existing = prev.find(i => i.item.id === item.id);\r\n      if (existing) {\r\n        return prev.map(i => \r\n          i.item.id === item.id ? {...i, quantity: i.quantity + 1} : i\r\n        );\r\n      }\r\n      return [...prev, { item, quantity: 1 }];\r\n    });\r\n  };\r\n\r\n  const handleRemoveItem = (id: string) => {\r\n    setInvoiceItems(prev => prev.filter(item => item.item.id !== id));\r\n  };\r\n\r\n  const total = invoiceItems.reduce(\r\n    (sum, item) => sum + (item.item.price * item.quantity), \r\n    0\r\n  );\r\n\r\n  return (\r\n    <div className=\"p-4 bg-amber-50 min-h-screen\">\r\n      <div className=\"flex justify-between items-center mb-6\">\r\n        <button \r\n          onClick={() => navigate('/')}\r\n          className=\"bg-amber-700 text-white px-4 py-2 rounded-lg\"\r\n        >\r\n          ← العودة\r\n        </button>\r\n        <h1 className=\"text-2xl font-bold text-amber-800\">\r\n          الطاولة رقم: {tableId}\r\n        </h1>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n        {/* قائمة المنتجات */}\r\n        <div className=\"lg:col-span-2\">\r\n          <h2 className=\"text-xl font-semibold mb-4 text-amber-700\">قائمة الطلبات</h2>\r\n          <div className=\"grid grid-cols-2 md:grid-cols-3 gap-3\">\r\n            {menuItems.map(item => (\r\n              <MenuItem \r\n                key={item.id} \r\n                item={item} \r\n                onAdd={() => handleAddItem(item)} \r\n              />\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* الفاتورة */}\r\n        <div className=\"bg-white p-4 rounded-lg shadow-lg\" id=\"invoice\">\r\n          <h2 className=\"text-xl font-semibold mb-4 text-amber-700\">فاتورة الطاولة</h2>\r\n          \r\n          <div className=\"mb-4 max-h-96 overflow-y-auto\">\r\n            {invoiceItems.length === 0 ? (\r\n              <p className=\"text-gray-500 text-center py-4\">لا توجد أصناف في الفاتورة</p>\r\n            ) : (\r\n              invoiceItems.map(invoiceItem => (\r\n                <InvoiceItem\r\n                  key={invoiceItem.item.id}\r\n                  item={invoiceItem.item}\r\n                  quantity={invoiceItem.quantity}\r\n                  onRemove={() => handleRemoveItem(invoiceItem.item.id)}\r\n                />\r\n              ))\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"border-t pt-3\">\r\n            <div className=\"flex justify-between font-bold text-lg\">\r\n              <span>المجموع:</span>\r\n              <span>{total.toLocaleString()} دينار</span>\r\n            </div>\r\n            \r\n            <button\r\n              onClick={() => window.print()}\r\n              className=\"w-full mt-4 bg-amber-600 text-white py-2 rounded-lg hover:bg-amber-700\"\r\n            >\r\n              طباعة الفاتورة\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default OrdersPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,SAAS,QAAsB,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IAAEC;EAAQ,CAAC,GAAGT,SAAS,CAAsB,CAAC;EACpD,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAA2C,EAAE,CAAC;EAE9F,MAAMc,aAAa,GAAIC,IAAkB,IAAK;IAC5CF,eAAe,CAACG,IAAI,IAAI;MACtB,MAAMC,QAAQ,GAAGD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,IAAI,CAACK,EAAE,KAAKL,IAAI,CAACK,EAAE,CAAC;MACtD,IAAIH,QAAQ,EAAE;QACZ,OAAOD,IAAI,CAACK,GAAG,CAACF,CAAC,IACfA,CAAC,CAACJ,IAAI,CAACK,EAAE,KAAKL,IAAI,CAACK,EAAE,GAAG;UAAC,GAAGD,CAAC;UAAEG,QAAQ,EAAEH,CAAC,CAACG,QAAQ,GAAG;QAAC,CAAC,GAAGH,CAC7D,CAAC;MACH;MACA,OAAO,CAAC,GAAGH,IAAI,EAAE;QAAED,IAAI;QAAEO,QAAQ,EAAE;MAAE,CAAC,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAIH,EAAU,IAAK;IACvCP,eAAe,CAACG,IAAI,IAAIA,IAAI,CAACQ,MAAM,CAACT,IAAI,IAAIA,IAAI,CAACA,IAAI,CAACK,EAAE,KAAKA,EAAE,CAAC,CAAC;EACnE,CAAC;EAED,MAAMK,KAAK,GAAGb,YAAY,CAACc,MAAM,CAC/B,CAACC,GAAG,EAAEZ,IAAI,KAAKY,GAAG,GAAIZ,IAAI,CAACA,IAAI,CAACa,KAAK,GAAGb,IAAI,CAACO,QAAS,EACtD,CACF,CAAC;EAED,oBACEf,OAAA;IAAKsB,SAAS,EAAC,8BAA8B;IAAAC,QAAA,gBAC3CvB,OAAA;MAAKsB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDvB,OAAA;QACEwB,OAAO,EAAEA,CAAA,KAAMpB,QAAQ,CAAC,GAAG,CAAE;QAC7BkB,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EACzD;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT5B,OAAA;QAAIsB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,GAAC,iEACnC,EAACpB,OAAO;MAAA;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEN5B,OAAA;MAAKsB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDvB,OAAA;QAAKsB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BvB,OAAA;UAAIsB,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5E5B,OAAA;UAAKsB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDzB,SAAS,CAACgB,GAAG,CAACN,IAAI,iBACjBR,OAAA,CAACJ,QAAQ;YAEPY,IAAI,EAAEA,IAAK;YACXqB,KAAK,EAAEA,CAAA,KAAMtB,aAAa,CAACC,IAAI;UAAE,GAF5BA,IAAI,CAACK,EAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGb,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5B,OAAA;QAAKsB,SAAS,EAAC,mCAAmC;QAACT,EAAE,EAAC,SAAS;QAAAU,QAAA,gBAC7DvB,OAAA;UAAIsB,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE7E5B,OAAA;UAAKsB,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAC3ClB,YAAY,CAACyB,MAAM,KAAK,CAAC,gBACxB9B,OAAA;YAAGsB,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,GAE3EvB,YAAY,CAACS,GAAG,CAACiB,WAAW,iBAC1B/B,OAAA,CAACH,WAAW;YAEVW,IAAI,EAAEuB,WAAW,CAACvB,IAAK;YACvBO,QAAQ,EAAEgB,WAAW,CAAChB,QAAS;YAC/BiB,QAAQ,EAAEA,CAAA,KAAMhB,gBAAgB,CAACe,WAAW,CAACvB,IAAI,CAACK,EAAE;UAAE,GAHjDkB,WAAW,CAACvB,IAAI,CAACK,EAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIzB,CACF;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN5B,OAAA;UAAKsB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BvB,OAAA;YAAKsB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDvB,OAAA;cAAAuB,QAAA,EAAM;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrB5B,OAAA;cAAAuB,QAAA,GAAOL,KAAK,CAACe,cAAc,CAAC,CAAC,EAAC,iCAAM;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eAEN5B,OAAA;YACEwB,OAAO,EAAEA,CAAA,KAAMU,MAAM,CAACC,KAAK,CAAC,CAAE;YAC9Bb,SAAS,EAAC,wEAAwE;YAAAC,QAAA,EACnF;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CA3FID,UAAoB;EAAA,QACJP,SAAS,EACZC,WAAW;AAAA;AAAAyC,EAAA,GAFxBnC,UAAoB;AA6F1B,eAAeA,UAAU;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}