{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\cafe-manager\\\\src\\\\components\\\\MenuItem.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MenuItem = ({\n  item,\n  onAdd\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: onAdd,\n    className: \"bg-white rounded-lg p-4 shadow-sm border border-amber-100 hover:shadow-md transition-shadow\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-start\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-right\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-semibold text-amber-900\",\n          children: item.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-amber-600 mt-1\",\n          children: [item.price.toLocaleString(), \" \\u062F\\u064A\\u0646\\u0627\\u0631\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-amber-100 rounded-full p-2\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          className: \"h-6 w-6 text-amber-600\",\n          fill: \"none\",\n          viewBox: \"0 0 24 24\",\n          stroke: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = MenuItem;\nexport default MenuItem;\nvar _c;\n$RefreshReg$(_c, \"MenuItem\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "MenuItem", "item", "onAdd", "onClick", "className", "children", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "price", "toLocaleString", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/cafe-manager/src/components/MenuItem.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { MenuItemType } from '../data/menuItems';\r\n\r\ninterface MenuItemProps {\r\n  item: MenuItemType;\r\n  onAdd: () => void;\r\n}\r\n\r\nconst MenuItem: React.FC<MenuItemProps> = ({ item, onAdd }) => {\r\n  return (\r\n    <button\r\n      onClick={onAdd}\r\n      className=\"bg-white rounded-lg p-4 shadow-sm border border-amber-100 hover:shadow-md transition-shadow\"\r\n    >\r\n      <div className=\"flex justify-between items-start\">\r\n        <div className=\"text-right\">\r\n          <h3 className=\"font-semibold text-amber-900\">{item.name}</h3>\r\n          <p className=\"text-amber-600 mt-1\">{item.price.toLocaleString()} دينار</p>\r\n        </div>\r\n        <div className=\"bg-amber-100 rounded-full p-2\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-amber-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\r\n          </svg>\r\n        </div>\r\n      </div>\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default MenuItem;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ1B,MAAMC,QAAiC,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAM,CAAC,KAAK;EAC7D,oBACEH,OAAA;IACEI,OAAO,EAAED,KAAM;IACfE,SAAS,EAAC,6FAA6F;IAAAC,QAAA,eAEvGN,OAAA;MAAKK,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/CN,OAAA;QAAKK,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBN,OAAA;UAAIK,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAEJ,IAAI,CAACK;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7DX,OAAA;UAAGK,SAAS,EAAC,qBAAqB;UAAAC,QAAA,GAAEJ,IAAI,CAACU,KAAK,CAACC,cAAc,CAAC,CAAC,EAAC,iCAAM;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACNX,OAAA;QAAKK,SAAS,EAAC,+BAA+B;QAAAC,QAAA,eAC5CN,OAAA;UAAKc,KAAK,EAAC,4BAA4B;UAACT,SAAS,EAAC,wBAAwB;UAACU,IAAI,EAAC,MAAM;UAACC,OAAO,EAAC,WAAW;UAACC,MAAM,EAAC,cAAc;UAAAX,QAAA,eAC9HN,OAAA;YAAMkB,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAA4B;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACW,EAAA,GAnBIrB,QAAiC;AAqBvC,eAAeA,QAAQ;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}