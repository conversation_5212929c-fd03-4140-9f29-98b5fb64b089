{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\cafe-manager\\\\src\\\\components\\\\InvoiceItem.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst InvoiceItem = ({\n  item,\n  quantity,\n  onRemove\n}) => {\n  const total = item.price * quantity;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex justify-between items-center py-3 border-b\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onRemove,\n        className: \"text-red-500 hover:text-red-700 ml-2\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          className: \"h-5 w-5\",\n          viewBox: \"0 0 20 20\",\n          fill: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-gray-500\",\n        children: [\"x\", quantity]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-right\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"font-medium\",\n        children: item.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-amber-600\",\n        children: [total.toLocaleString(), \" \\u062F\\u064A\\u0646\\u0627\\u0631\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_c = InvoiceItem;\nexport default InvoiceItem;\nvar _c;\n$RefreshReg$(_c, \"InvoiceItem\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "InvoiceItem", "item", "quantity", "onRemove", "total", "price", "className", "children", "onClick", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/cafe-manager/src/components/InvoiceItem.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { MenuItemType } from '../data/menuItems';\r\n\r\ninterface InvoiceItemProps {\r\n  item: MenuItemType;\r\n  quantity: number;\r\n  onRemove: () => void;\r\n}\r\n\r\nconst InvoiceItem: React.FC<InvoiceItemProps> = ({ item, quantity, onRemove }) => {\r\n  const total = item.price * quantity;\r\n\r\n  return (\r\n    <div className=\"flex justify-between items-center py-3 border-b\">\r\n      <div className=\"flex items-center\">\r\n        <button \r\n          onClick={onRemove}\r\n          className=\"text-red-500 hover:text-red-700 ml-2\"\r\n        >\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n            <path fillRule=\"evenodd\" d=\"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n          </svg>\r\n        </button>\r\n        <span className=\"text-gray-500\">x{quantity}</span>\r\n      </div>\r\n      \r\n      <div className=\"text-right\">\r\n        <p className=\"font-medium\">{item.name}</p>\r\n        <p className=\"text-amber-600\">{total.toLocaleString()} دينار</p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InvoiceItem;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS1B,MAAMC,WAAuC,GAAGA,CAAC;EAAEC,IAAI;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAChF,MAAMC,KAAK,GAAGH,IAAI,CAACI,KAAK,GAAGH,QAAQ;EAEnC,oBACEH,OAAA;IAAKO,SAAS,EAAC,iDAAiD;IAAAC,QAAA,gBAC9DR,OAAA;MAAKO,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCR,OAAA;QACES,OAAO,EAAEL,QAAS;QAClBG,SAAS,EAAC,sCAAsC;QAAAC,QAAA,eAEhDR,OAAA;UAAKU,KAAK,EAAC,4BAA4B;UAACH,SAAS,EAAC,SAAS;UAACI,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,cAAc;UAAAJ,QAAA,eACjGR,OAAA;YAAMa,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,6MAA6M;YAACC,QAAQ,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3P;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACTnB,OAAA;QAAMO,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAC,GAAC,EAACL,QAAQ;MAAA;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC,eAENnB,OAAA;MAAKO,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBR,OAAA;QAAGO,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAEN,IAAI,CAACkB;MAAI;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1CnB,OAAA;QAAGO,SAAS,EAAC,gBAAgB;QAAAC,QAAA,GAAEH,KAAK,CAACgB,cAAc,CAAC,CAAC,EAAC,iCAAM;MAAA;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GAvBIrB,WAAuC;AAyB7C,eAAeA,WAAW;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}