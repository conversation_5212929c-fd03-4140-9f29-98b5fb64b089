import React from 'react';

interface TableCardProps {
  tableNumber: number;
  onClick: () => void;
}

const TableCard: React.FC<TableCardProps> = ({ tableNumber, onClick }) => {
  return (
    <button
      onClick={onClick}
      className="bg-white rounded-xl shadow-md p-6 flex flex-col items-center justify-center 
                 border-2 border-amber-200 hover:border-amber-400 transition-all duration-200"
    >
      <div className="bg-amber-100 rounded-full p-3 mb-2">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
        </svg>
      </div>
      <span className="text-xl font-semibold text-amber-800">طاولة {tableNumber}</span>
    </button>
  );
};

export default TableCard;