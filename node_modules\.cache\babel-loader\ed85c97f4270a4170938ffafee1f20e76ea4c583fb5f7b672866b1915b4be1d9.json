{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\cafe-manager\\\\src\\\\pages\\\\TablesPage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport TableCard from '../components/TableCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TablesPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const tables = Array.from({\n    length: 10\n  }, (_, i) => i + 1);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-4 bg-amber-50 min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold text-center text-amber-800 mb-8\",\n      children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0637\\u0644\\u0628\\u0627\\u062A \\u0627\\u0644\\u0645\\u0642\\u0647\\u0649\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4\",\n      children: tables.map(table => /*#__PURE__*/_jsxDEV(TableCard, {\n        tableNumber: table,\n        onClick: () => navigate(`/orders/${table}`)\n      }, table, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_s(TablesPage, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = TablesPage;\nexport default TablesPage;\nvar _c;\n$RefreshReg$(_c, \"TablesPage\");", "map": {"version": 3, "names": ["React", "useNavigate", "TableCard", "jsxDEV", "_jsxDEV", "TablesPage", "_s", "navigate", "tables", "Array", "from", "length", "_", "i", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "table", "tableNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/cafe-manager/src/pages/TablesPage.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport TableCard from '../components/TableCard';\r\n\r\nconst TablesPage: React.FC = () => {\r\n  const navigate = useNavigate();\r\n  const tables = Array.from({ length: 10 }, (_, i) => i + 1);\r\n\r\n  return (\r\n    <div className=\"p-4 bg-amber-50 min-h-screen\">\r\n      <h1 className=\"text-3xl font-bold text-center text-amber-800 mb-8\">\r\n        إدارة طلبات المقهى\r\n      </h1>\r\n      \r\n      <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4\">\r\n        {tables.map(table => (\r\n          <TableCard \r\n            key={table} \r\n            tableNumber={table} \r\n            onClick={() => navigate(`/orders/${table}`)} \r\n          />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TablesPage;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,SAAS,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,MAAM,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;EAE1D,oBACET,OAAA;IAAKU,SAAS,EAAC,8BAA8B;IAAAC,QAAA,gBAC3CX,OAAA;MAAIU,SAAS,EAAC,oDAAoD;MAAAC,QAAA,EAAC;IAEnE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELf,OAAA;MAAKU,SAAS,EAAC,qEAAqE;MAAAC,QAAA,EACjFP,MAAM,CAACY,GAAG,CAACC,KAAK,iBACfjB,OAAA,CAACF,SAAS;QAERoB,WAAW,EAAED,KAAM;QACnBE,OAAO,EAAEA,CAAA,KAAMhB,QAAQ,CAAC,WAAWc,KAAK,EAAE;MAAE,GAFvCA,KAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGX,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACb,EAAA,CArBID,UAAoB;EAAA,QACPJ,WAAW;AAAA;AAAAuB,EAAA,GADxBnB,UAAoB;AAuB1B,eAAeA,UAAU;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}