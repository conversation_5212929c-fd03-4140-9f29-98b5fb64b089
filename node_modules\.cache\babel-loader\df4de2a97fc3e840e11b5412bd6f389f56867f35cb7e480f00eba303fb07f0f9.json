{"ast": null, "code": "export const menuItems = [{\n  id: '1',\n  name: 'شاي عربي',\n  price: 1500,\n  category: 'مشروبات ساخنة'\n}, {\n  id: '2',\n  name: 'قهوة تركية',\n  price: 2000,\n  category: 'مشروبات ساخنة'\n}, {\n  id: '3',\n  name: 'نركيلة تفاحة',\n  price: 8000,\n  category: 'نركيلة'\n}, {\n  id: '4',\n  name: 'نركيلة عنب',\n  price: 8000,\n  category: 'نركيلة'\n}, {\n  id: '5',\n  name: 'كابتشينو',\n  price: 2500,\n  category: 'مشروبات ساخنة'\n}, {\n  id: '6',\n  name: 'عصير برتقال',\n  price: 3000,\n  category: 'عصائر'\n}, {\n  id: '7',\n  name: 'ميلك شيك',\n  price: 3500,\n  category: 'مشروبات باردة'\n}, {\n  id: '8',\n  name: 'ساندويش جبنة',\n  price: 2500,\n  category: 'وجبات خفيفة'\n}, {\n  id: '9',\n  name: 'كيك شوكولاتة',\n  price: 2000,\n  category: 'حلويات'\n}, {\n  id: '10',\n  name: 'ماء معدني',\n  price: 1000,\n  category: 'مشروبات باردة'\n}];", "map": {"version": 3, "names": ["menuItems", "id", "name", "price", "category"], "sources": ["C:/Users/<USER>/Downloads/cafe-manager/src/data/menuItems.ts"], "sourcesContent": ["export type MenuItemType = {\r\n  id: string;\r\n  name: string;\r\n  price: number;\r\n  category: string;\r\n};\r\n\r\nexport const menuItems: MenuItemType[] = [\r\n  { id: '1', name: 'شاي عربي', price: 1500, category: 'مشروبات ساخنة' },\r\n  { id: '2', name: 'قهوة تركية', price: 2000, category: 'مشروبات ساخنة' },\r\n  { id: '3', name: 'نركيلة تفاحة', price: 8000, category: 'نركيلة' },\r\n  { id: '4', name: 'نركيلة عنب', price: 8000, category: 'نركيلة' },\r\n  { id: '5', name: 'كابتشينو', price: 2500, category: 'مشروبات ساخنة' },\r\n  { id: '6', name: 'عصير برتقال', price: 3000, category: 'عصائر' },\r\n  { id: '7', name: 'ميلك شيك', price: 3500, category: 'مشروبات باردة' },\r\n  { id: '8', name: 'ساندويش جبنة', price: 2500, category: 'وجبات خفيفة' },\r\n  { id: '9', name: 'كيك شوكولاتة', price: 2000, category: 'حلويات' },\r\n  { id: '10', name: 'ماء معدني', price: 1000, category: 'مشروبات باردة' },\r\n];"], "mappings": "AAOA,OAAO,MAAMA,SAAyB,GAAG,CACvC;EAAEC,EAAE,EAAE,GAAG;EAAEC,IAAI,EAAE,UAAU;EAAEC,KAAK,EAAE,IAAI;EAAEC,QAAQ,EAAE;AAAgB,CAAC,EACrE;EAAEH,EAAE,EAAE,GAAG;EAAEC,IAAI,EAAE,YAAY;EAAEC,KAAK,EAAE,IAAI;EAAEC,QAAQ,EAAE;AAAgB,CAAC,EACvE;EAAEH,EAAE,EAAE,GAAG;EAAEC,IAAI,EAAE,cAAc;EAAEC,KAAK,EAAE,IAAI;EAAEC,QAAQ,EAAE;AAAS,CAAC,EAClE;EAAEH,EAAE,EAAE,GAAG;EAAEC,IAAI,EAAE,YAAY;EAAEC,KAAK,EAAE,IAAI;EAAEC,QAAQ,EAAE;AAAS,CAAC,EAChE;EAAEH,EAAE,EAAE,GAAG;EAAEC,IAAI,EAAE,UAAU;EAAEC,KAAK,EAAE,IAAI;EAAEC,QAAQ,EAAE;AAAgB,CAAC,EACrE;EAAEH,EAAE,EAAE,GAAG;EAAEC,IAAI,EAAE,aAAa;EAAEC,KAAK,EAAE,IAAI;EAAEC,QAAQ,EAAE;AAAQ,CAAC,EAChE;EAAEH,EAAE,EAAE,GAAG;EAAEC,IAAI,EAAE,UAAU;EAAEC,KAAK,EAAE,IAAI;EAAEC,QAAQ,EAAE;AAAgB,CAAC,EACrE;EAAEH,EAAE,EAAE,GAAG;EAAEC,IAAI,EAAE,cAAc;EAAEC,KAAK,EAAE,IAAI;EAAEC,QAAQ,EAAE;AAAc,CAAC,EACvE;EAAEH,EAAE,EAAE,GAAG;EAAEC,IAAI,EAAE,cAAc;EAAEC,KAAK,EAAE,IAAI;EAAEC,QAAQ,EAAE;AAAS,CAAC,EAClE;EAAEH,EAAE,EAAE,IAAI;EAAEC,IAAI,EAAE,WAAW;EAAEC,KAAK,EAAE,IAAI;EAAEC,QAAQ,EAAE;AAAgB,CAAC,CACxE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}