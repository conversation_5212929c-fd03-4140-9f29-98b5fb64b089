[{"C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\pages\\OrdersPage.tsx": "3", "C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\pages\\TablesPage.tsx": "4", "C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\data\\menuItems.ts": "5", "C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\components\\MenuItem.tsx": "6", "C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\components\\InvoiceItem.tsx": "7", "C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\components\\TableCard.tsx": "8"}, {"size": 284, "mtime": 1752337249017, "results": "9", "hashOfConfig": "10"}, {"size": 459, "mtime": 1752337214014, "results": "11", "hashOfConfig": "10"}, {"size": 3531, "mtime": 1752337398677, "results": "12", "hashOfConfig": "10"}, {"size": 829, "mtime": 1752339784065, "results": "13", "hashOfConfig": "10"}, {"size": 1067, "mtime": 1752337517277, "results": "14", "hashOfConfig": "10"}, {"size": 1077, "mtime": 1752337444447, "results": "15", "hashOfConfig": "10"}, {"size": 1288, "mtime": 1752337479973, "results": "16", "hashOfConfig": "10"}, {"size": 921, "mtime": 1752339670855, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mw20h5", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\pages\\OrdersPage.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\pages\\TablesPage.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\data\\menuItems.ts", [], [], "C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\components\\MenuItem.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\components\\InvoiceItem.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\cafe-manager\\src\\components\\TableCard.tsx", [], []]