import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import TablesPage from './pages/TablesPage';
import OrdersPage from './pages/OrdersPage';

const App: React.FC = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<TablesPage />} />
        <Route path="/orders/:tableId" element={<OrdersPage />} />
      </Routes>
    </BrowserRouter>
  );
};

export default App;