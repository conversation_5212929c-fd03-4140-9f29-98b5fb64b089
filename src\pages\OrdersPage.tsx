import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import MenuItem from '../components/MenuItem';
import InvoiceItem from '../components/InvoiceItem';
import { menuItems, MenuItemType } from '../data/menuItems';

const OrdersPage: React.FC = () => {
  const { tableId } = useParams<{ tableId: string }>();
  const navigate = useNavigate();
  const [invoiceItems, setInvoiceItems] = useState<{item: MenuItemType, quantity: number}[]>([]);

  const handleAddItem = (item: MenuItemType) => {
    setInvoiceItems(prev => {
      const existing = prev.find(i => i.item.id === item.id);
      if (existing) {
        return prev.map(i => 
          i.item.id === item.id ? {...i, quantity: i.quantity + 1} : i
        );
      }
      return [...prev, { item, quantity: 1 }];
    });
  };

  const handleRemoveItem = (id: string) => {
    setInvoiceItems(prev => prev.filter(item => item.item.id !== id));
  };

  const total = invoiceItems.reduce(
    (sum, item) => sum + (item.item.price * item.quantity), 
    0
  );

  return (
    <div className="p-4 bg-amber-50 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <button 
          onClick={() => navigate('/')}
          className="bg-amber-700 text-white px-4 py-2 rounded-lg"
        >
          ← العودة
        </button>
        <h1 className="text-2xl font-bold text-amber-800">
          الطاولة رقم: {tableId}
        </h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* قائمة المنتجات */}
        <div className="lg:col-span-2">
          <h2 className="text-xl font-semibold mb-4 text-amber-700">قائمة الطلبات</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {menuItems.map(item => (
              <MenuItem 
                key={item.id} 
                item={item} 
                onAdd={() => handleAddItem(item)} 
              />
            ))}
          </div>
        </div>

        {/* الفاتورة */}
        <div className="bg-white p-4 rounded-lg shadow-lg" id="invoice">
          <h2 className="text-xl font-semibold mb-4 text-amber-700">فاتورة الطاولة</h2>
          
          <div className="mb-4 max-h-96 overflow-y-auto">
            {invoiceItems.length === 0 ? (
              <p className="text-gray-500 text-center py-4">لا توجد أصناف في الفاتورة</p>
            ) : (
              invoiceItems.map(invoiceItem => (
                <InvoiceItem
                  key={invoiceItem.item.id}
                  item={invoiceItem.item}
                  quantity={invoiceItem.quantity}
                  onRemove={() => handleRemoveItem(invoiceItem.item.id)}
                />
              ))
            )}
          </div>

          <div className="border-t pt-3">
            <div className="flex justify-between font-bold text-lg">
              <span>المجموع:</span>
              <span>{total.toLocaleString()} دينار</span>
            </div>
            
            <button
              onClick={() => window.print()}
              className="w-full mt-4 bg-amber-600 text-white py-2 rounded-lg hover:bg-amber-700"
            >
              طباعة الفاتورة
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrdersPage;