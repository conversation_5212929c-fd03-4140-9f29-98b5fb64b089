import React from 'react';
import { useNavigate } from 'react-router-dom';
import TableCard from '../components/TableCard';

const TablesPage: React.FC = () => {
  const navigate = useNavigate();
  const tables = Array.from({ length: 10 }, (_, i) => i + 1);

  return (
    <div className="p-4 bg-amber-50 min-h-screen">
      <h1 className="text-3xl font-bold text-center text-amber-800 mb-8">
        إدارة طلبات المقهى
      </h1>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
        {tables.map(table => (
          <TableCard 
            key={table} 
            tableNumber={table} 
            onClick={() => navigate(`/orders/${table}`)} 
          />
        ))}
      </div>
    </div>
  );
};

export default TablesPage;