import React from 'react';
import { MenuItemType } from '../data/menuItems';

interface MenuItemProps {
  item: MenuItemType;
  onAdd: () => void;
}

const MenuItem: React.FC<MenuItemProps> = ({ item, onAdd }) => {
  return (
    <button
      onClick={onAdd}
      className="bg-white rounded-lg p-4 shadow-sm border border-amber-100 hover:shadow-md transition-shadow"
    >
      <div className="flex justify-between items-start">
        <div className="text-right">
          <h3 className="font-semibold text-amber-900">{item.name}</h3>
          <p className="text-amber-600 mt-1">{item.price.toLocaleString()} دينار</p>
        </div>
        <div className="bg-amber-100 rounded-full p-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </div>
      </div>
    </button>
  );
};

export default MenuItem;