{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\cafe-manager\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter, Routes, Route } from 'react-router-dom';\nimport TablesPage from './pages/TablesPage';\nimport OrdersPage from './pages/OrdersPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(TablesPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/orders/:tableId\",\n        element: /*#__PURE__*/_jsxDEV(OrdersPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "TablesPage", "OrdersPage", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/cafe-manager/src/App.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { BrowserRouter, Routes, Route } from 'react-router-dom';\r\nimport TablesPage from './pages/TablesPage';\r\nimport OrdersPage from './pages/OrdersPage';\r\n\r\nconst App: React.FC = () => {\r\n  return (\r\n    <BrowserRouter>\r\n      <Routes>\r\n        <Route path=\"/\" element={<TablesPage />} />\r\n        <Route path=\"/orders/:tableId\" element={<OrdersPage />} />\r\n      </Routes>\r\n    </BrowserRouter>\r\n  );\r\n};\r\n\r\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAC/D,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,UAAU,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACED,OAAA,CAACN,aAAa;IAAAQ,QAAA,eACZF,OAAA,CAACL,MAAM;MAAAO,QAAA,gBACLF,OAAA,CAACJ,KAAK;QAACO,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEJ,OAAA,CAACH,UAAU;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3CR,OAAA,CAACJ,KAAK;QAACO,IAAI,EAAC,kBAAkB;QAACC,OAAO,eAAEJ,OAAA,CAACF,UAAU;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB,CAAC;AAACC,EAAA,GATIR,GAAa;AAWnB,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}