{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\cafe-manager\\\\src\\\\components\\\\TableCard.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TableCard = ({\n  tableNumber,\n  onClick\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: onClick,\n    className: \"bg-white rounded-xl shadow-md p-6 flex flex-col items-center justify-center \\r border-2 border-amber-200 hover:border-amber-400 transition-all duration-200\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-amber-100 rounded-full p-3 mb-2\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"h-10 w-10 text-amber-600\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: 2,\n          d: \"M4 6h16M4 12h16m-7 6h7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"text-xl font-semibold text-amber-800\",\n      children: [\"\\u0637\\u0627\\u0648\\u0644\\u0629 \", tableNumber]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = TableCard;\nexport default TableCard;\nvar _c;\n$RefreshReg$(_c, \"TableCard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "TableCard", "tableNumber", "onClick", "className", "children", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/cafe-manager/src/components/TableCard.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\ninterface TableCardProps {\r\n  tableNumber: number;\r\n  onClick: () => void;\r\n}\r\n\r\nconst TableCard: React.FC<TableCardProps> = ({ tableNumber, onClick }) => {\r\n  return (\r\n    <button\r\n      onClick={onClick}\r\n      className=\"bg-white rounded-xl shadow-md p-6 flex flex-col items-center justify-center \r\n                 border-2 border-amber-200 hover:border-amber-400 transition-all duration-200\"\r\n    >\r\n      <div className=\"bg-amber-100 rounded-full p-3 mb-2\">\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-10 w-10 text-amber-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16m-7 6h7\" />\r\n        </svg>\r\n      </div>\r\n      <span className=\"text-xl font-semibold text-amber-800\">طاولة {tableNumber}</span>\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default TableCard;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO1B,MAAMC,SAAmC,GAAGA,CAAC;EAAEC,WAAW;EAAEC;AAAQ,CAAC,KAAK;EACxE,oBACEH,OAAA;IACEG,OAAO,EAAEA,OAAQ;IACjBC,SAAS,EAAC,6JAC8E;IAAAC,QAAA,gBAExFL,OAAA;MAAKI,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDL,OAAA;QAAKM,KAAK,EAAC,4BAA4B;QAACF,SAAS,EAAC,0BAA0B;QAACG,IAAI,EAAC,MAAM;QAACC,OAAO,EAAC,WAAW;QAACC,MAAM,EAAC,cAAc;QAAAJ,QAAA,eAChIL,OAAA;UAAMU,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC,OAAO;UAACC,WAAW,EAAE,CAAE;UAACC,CAAC,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNjB,OAAA;MAAMI,SAAS,EAAC,sCAAsC;MAAAC,QAAA,GAAC,iCAAM,EAACH,WAAW;IAAA;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3E,CAAC;AAEb,CAAC;AAACC,EAAA,GAfIjB,SAAmC;AAiBzC,eAAeA,SAAS;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}